import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { PortalV4ApiServiceImpl } from './portal-v4-api.service';
import { SourceModule } from '@core/source/source.module';
import { PORTAL_V4_TARGET_SERVICE, PortalV4TargetServiceImpl } from '@core/target/portal-v4/portal-v4-target.service';
import { PORTAL_V4_SERVICE } from '@core/target/portal-v4/portal-v4.service.interface';
import { MimeTypeResolverService } from '@core/mime-type-resolver.service';

/**
 * Module for EmmySoft Portal V4 Integration API
 */
@Module({
  imports: [HttpModule, ConfigModule, SourceModule],
  providers: [
    {
      provide: PORTAL_V4_SERVICE,
      useClass: PortalV4ApiServiceImpl,
    },
    {
      provide: PORTAL_V4_TARGET_SERVICE,
      useClass: PortalV4TargetServiceImpl,
    },
    MimeTypeResolverService,
  ],
  exports: [PORTAL_V4_TARGET_SERVICE],
})
export class PortalV4Module {}
