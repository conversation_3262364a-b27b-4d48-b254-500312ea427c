import {
  Status,
  CreateStatusInput,
  UpdateStatusInput,
  Contact,
  CreateContactInput,
  UpdateContactInput,
  Account,
  CreateAccountInput,
  UpdateAccountInput,
  Project,
  CreateProjectInput,
  UpdateProjectInput,
  Candidate,
  CreateCandidateInput,
  UpdateCandidateInput,
  JobApplication,
  CreateJobApplicationInput,
  UpdateJobApplicationInput,
  Document,
  PaginatedResponse,
  CreateDocumentInput,
  UpdateDocumentInput,
} from './portal-v4.types';
import { TenantConfig } from '@core/env.model';

export const PORTAL_V4_SERVICE = Symbol('PORTAL_V4_SERVICE');

/**
 * Interface for the EmmySoft Portal V4 Integration API Service
 */
export interface IPortalV4Service {
  // Status API methods
  getStatuses(tenant: TenantConfig, page?: number, pageSize?: number): Promise<PaginatedResponse<Status>>;
  getStatusById(tenant: TenantConfig, id: string): Promise<Status>;
  createStatus(tenant: TenantConfig, data: CreateStatusInput): Promise<Status>;
  updateStatus(tenant: TenantConfig, id: string, data: UpdateStatusInput): Promise<Status>;
  deleteStatus(tenant: TenantConfig, id: string): Promise<void>;

  // Contact API methods
  getContacts(tenant: TenantConfig, page?: number, pageSize?: number): Promise<PaginatedResponse<Contact>>;
  getContactById(tenant: TenantConfig, id: string): Promise<Contact>;
  createContact(tenant: TenantConfig, data: CreateContactInput): Promise<Contact>;
  updateContact(tenant: TenantConfig, id: string, data: UpdateContactInput): Promise<Contact>;
  deleteContact(tenant: TenantConfig, id: string): Promise<void>;

  // Account API methods
  getAccounts(tenant: TenantConfig, page?: number, pageSize?: number): Promise<PaginatedResponse<Account>>;
  getAccountById(tenant: TenantConfig, id: string): Promise<Account>;
  createAccount(tenant: TenantConfig, data: CreateAccountInput): Promise<Account>;
  updateAccount(tenant: TenantConfig, id: string, data: UpdateAccountInput): Promise<Account>;
  deleteAccount(tenant: TenantConfig, id: string): Promise<void>;

  // Project API methods
  getProjects(tenant: TenantConfig, page?: number, pageSize?: number): Promise<PaginatedResponse<Project>>;
  getProjectById(tenant: TenantConfig, id: string): Promise<Project>;
  createProject(tenant: TenantConfig, data: CreateProjectInput): Promise<Project>;
  updateProject(tenant: TenantConfig, id: string, data: UpdateProjectInput): Promise<Project>;
  deleteProject(tenant: TenantConfig, id: string): Promise<void>;

  // Candidate API methods
  getCandidates(
    tenant: TenantConfig,
    page?: number,
    pageSize?: number,
    filter?: Record<string, any>,
  ): Promise<PaginatedResponse<Candidate>>;
  getCandidateById(tenant: TenantConfig, id: string): Promise<Candidate>;
  createCandidate(tenant: TenantConfig, data: CreateCandidateInput): Promise<Candidate>;
  updateCandidate(tenant: TenantConfig, id: string, data: UpdateCandidateInput): Promise<Candidate>;
  deleteCandidate(tenant: TenantConfig, id: string): Promise<void>;

  // JobApplication API methods
  getJobApplications(
    tenant: TenantConfig,
    page?: number,
    pageSize?: number,
  ): Promise<PaginatedResponse<JobApplication>>;
  getJobApplicationById(tenant: TenantConfig, id: string): Promise<JobApplication>;
  createJobApplication(tenant: TenantConfig, data: CreateJobApplicationInput): Promise<JobApplication>;
  updateJobApplication(tenant: TenantConfig, id: string, data: UpdateJobApplicationInput): Promise<JobApplication>;
  deleteJobApplication(tenant: TenantConfig, id: string): Promise<void>;

  // Document API methods
  createDocument(tenant: TenantConfig, input: CreateDocumentInput): Promise<Document>;
  updateDocument(tenant: TenantConfig, id: string, input: Partial<UpdateDocumentInput>): Promise<Document>;
  deleteDocument(tenant: TenantConfig, id: string): Promise<void>;
  getDocumentById(tenant: TenantConfig, id: string): Promise<Document>;

  // Additional reference methods
  getStatusByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Status>;
  getStatusesByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Status[]>;
  getContactByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Contact>;
  getContactsByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Contact[]>;
  getAccountByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Account>;
  getAccountsByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Account[]>;
  getProjectByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Project>;
  getProjectsByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Project[]>;
  getCandidateByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Candidate>;
  getCandidatesByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Candidate[]>;
  getJobApplicationByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<JobApplication>;
  getJobApplicationsByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<JobApplication[]>;
  getDocumentByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Document>;
  getDocumentsByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
    page?: number,
    pageSize?: number,
  ): Promise<PaginatedResponse<Document>>;
}
