import { HttpException, Injectable, InternalServerErrorException } from '@nestjs/common';
import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ClsService } from 'nestjs-cls';
import { EnhancedLoggerService } from '@infrastructure/logger/enhanced-logger.service';
import { TenantConfig } from '@core/env.model';
import {
  Account,
  Candidate,
  Contact,
  CreateAccountInput,
  CreateCandidateInput,
  CreateContactInput,
  CreateDocumentInput,
  CreateJobApplicationInput,
  CreateProjectInput,
  CreateStatusInput,
  Document,
  JobApplication,
  PaginatedResponse,
  Project,
  Status,
  UpdateAccountInput,
  UpdateCandidateInput,
  UpdateContactInput,
  UpdateDocumentInput,
  UpdateJobApplicationInput,
  UpdateProjectInput,
  UpdateStatusInput,
} from './portal-v4.types';
import { IPortalV4Service } from '@core/target/portal-v4/portal-v4.service.interface';

/**
 * Service for interacting with the EmmySoft Portal V4 Integration API
 */
@Injectable()
export class PortalV4ApiServiceImpl implements IPortalV4Service {
  private axiosInstances: { [tenantId: number]: AxiosInstance } = {};

  constructor(private readonly logger: EnhancedLoggerService, private readonly cls: ClsService) {
    this.logger.setContext(PortalV4ApiServiceImpl.name);
  }

  /**
   * Get all statuses
   */
  async getStatuses(tenant: TenantConfig, page = 1, pageSize = 20): Promise<PaginatedResponse<Status>> {
    this.logger.debug({ tenantId: tenant.id, page, pageSize }, 'Getting paginated list of statuses');
    return this.request<PaginatedResponse<Status>>(tenant, 'GET', '/statuses', undefined, {
      params: { page, pageSize },
    });
  }

  /**
   * Get status by ID
   */
  async getStatusById(tenant: TenantConfig, id: string): Promise<Status> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Getting status by ID');
    return this.request<Status>(tenant, 'GET', `/statuses/${id}`);
  }

  /**
   * Create a new status
   */
  async createStatus(tenant: TenantConfig, data: CreateStatusInput): Promise<Status> {
    this.logger.debug({ tenantId: tenant.id, data }, 'Creating new status');
    return this.request<Status>(tenant, 'POST', '/statuses', data);
  }

  /**
   * Update an existing status
   */
  async updateStatus(tenant: TenantConfig, id: string, data: UpdateStatusInput): Promise<Status> {
    this.logger.debug({ tenantId: tenant.id, id, data }, 'Updating status');
    return this.request<Status>(tenant, 'PATCH', `/statuses/${id}`, data);
  }

  // Status API methods

  /**
   * Delete a status
   */
  async deleteStatus(tenant: TenantConfig, id: string): Promise<void> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Deleting status');
    await this.request(tenant, 'DELETE', `/statuses/${id}`);
  }

  /**
   * Get all contacts
   */
  async getContacts(tenant: TenantConfig, page = 1, pageSize = 20): Promise<PaginatedResponse<Contact>> {
    this.logger.debug({ tenantId: tenant.id, page, pageSize }, 'Getting paginated list of contacts');
    return this.request<PaginatedResponse<Contact>>(tenant, 'GET', '/contacts', undefined, {
      params: { page, pageSize },
    });
  }

  /**
   * Get contact by ID
   */
  async getContactById(tenant: TenantConfig, id: string): Promise<Contact> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Getting contact by ID');
    return this.request<Contact>(tenant, 'GET', `/contacts/${id}`);
  }

  /**
   * Create a new contact
   */
  async createContact(tenant: TenantConfig, data: CreateContactInput): Promise<Contact> {
    this.logger.debug({ tenantId: tenant.id, data }, 'Creating new contact');
    return this.request<Contact>(tenant, 'POST', '/contacts', data);
  }

  /**
   * Update an existing contact
   */
  async updateContact(tenant: TenantConfig, id: string, data: UpdateContactInput): Promise<Contact> {
    this.logger.debug({ tenantId: tenant.id, id, data }, 'Updating contact');
    return this.request<Contact>(tenant, 'PATCH', `/contacts/${id}`, data);
  }

  // Contact API methods

  /**
   * Delete a contact
   */
  async deleteContact(tenant: TenantConfig, id: string): Promise<void> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Deleting contact');
    await this.request(tenant, 'DELETE', `/contacts/${id}`);
  }

  /**
   * Get all accounts
   */
  async getAccounts(tenant: TenantConfig, page = 1, pageSize = 20): Promise<PaginatedResponse<Account>> {
    this.logger.debug({ tenantId: tenant.id, page, pageSize }, 'Getting paginated list of accounts');
    return this.request<PaginatedResponse<Account>>(tenant, 'GET', '/accounts', undefined, {
      params: { page, pageSize },
    });
  }

  /**
   * Get account by ID
   */
  async getAccountById(tenant: TenantConfig, id: string): Promise<Account> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Getting account by ID');
    return this.request<Account>(tenant, 'GET', `/accounts/${id}`);
  }

  /**
   * Create a new account
   */
  async createAccount(tenant: TenantConfig, data: CreateAccountInput): Promise<Account> {
    this.logger.debug({ tenantId: tenant.id, data }, 'Creating new account');
    return this.request<Account>(tenant, 'POST', '/accounts', data);
  }

  /**
   * Update an existing account
   */
  async updateAccount(tenant: TenantConfig, id: string, data: UpdateAccountInput): Promise<Account> {
    this.logger.debug({ tenantId: tenant.id, id, data }, 'Updating account');
    return this.request<Account>(tenant, 'PATCH', `/accounts/${id}`, data);
  }

  // Account API methods

  /**
   * Delete an account
   */
  async deleteAccount(tenant: TenantConfig, id: string): Promise<void> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Deleting account');
    await this.request(tenant, 'DELETE', `/accounts/${id}`);
  }

  /**
   * Get all projects
   */
  async getProjects(tenant: TenantConfig, page = 1, pageSize = 20): Promise<PaginatedResponse<Project>> {
    this.logger.debug({ tenantId: tenant.id, page, pageSize }, 'Getting paginated list of projects');
    return this.request<PaginatedResponse<Project>>(tenant, 'GET', '/projects', undefined, {
      params: { page, pageSize },
    });
  }

  /**
   * Get project by ID
   */
  async getProjectById(tenant: TenantConfig, id: string): Promise<Project> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Getting project by ID');
    return this.request<Project>(tenant, 'GET', `/projects/${id}`);
  }

  /**
   * Create a new project
   */
  async createProject(tenant: TenantConfig, data: CreateProjectInput): Promise<Project> {
    this.logger.debug({ tenantId: tenant.id, data }, 'Creating new project');
    return this.request<Project>(tenant, 'POST', '/projects', data);
  }

  /**
   * Update an existing project
   */
  async updateProject(tenant: TenantConfig, id: string, data: UpdateProjectInput): Promise<Project> {
    this.logger.debug({ tenantId: tenant.id, id, data }, 'Updating project');
    return this.request<Project>(tenant, 'PATCH', `/projects/${id}`, data);
  }

  // Project API methods

  /**
   * Delete a project
   */
  async deleteProject(tenant: TenantConfig, id: string): Promise<void> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Deleting project');
    await this.request(tenant, 'DELETE', `/projects/${id}`);
  }

  /**
   * Get all candidates
   */
  async getCandidates(
    tenant: TenantConfig,
    page = 1,
    pageSize = 20,
    filter?: Record<string, any>,
  ): Promise<PaginatedResponse<Candidate>> {
    this.logger.debug({ tenantId: tenant.id, page, pageSize, filter }, 'Getting paginated list of candidates');
    return this.request<PaginatedResponse<Candidate>>(tenant, 'GET', '/candidates', undefined, {
      params: { page, pageSize, ...filter },
    });
  }

  /**
   * Get candidate by ID
   */
  async getCandidateById(tenant: TenantConfig, id: string): Promise<Candidate> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Getting candidate by ID');
    return this.request<Candidate>(tenant, 'GET', `/candidates/${id}`);
  }

  /**
   * Create a new candidate
   */
  async createCandidate(tenant: TenantConfig, data: CreateCandidateInput): Promise<Candidate> {
    this.logger.debug({ tenantId: tenant.id, data }, 'Creating new candidate');
    return this.request<Candidate>(tenant, 'POST', '/candidates', data);
  }

  /**
   * Update an existing candidate
   */
  async updateCandidate(tenant: TenantConfig, id: string, data: UpdateCandidateInput): Promise<Candidate> {
    this.logger.debug({ tenantId: tenant.id, id, data }, 'Updating candidate');
    return this.request<Candidate>(tenant, 'PUT', `/candidates/${id}`, data);
  }

  // Candidate API methods

  /**
   * Delete a candidate
   */
  async deleteCandidate(tenant: TenantConfig, id: string): Promise<void> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Deleting candidate');
    await this.request(tenant, 'DELETE', `/candidates/${id}`);
  }

  /**
   * Get all job applications
   */
  async getJobApplications(tenant: TenantConfig, page = 1, pageSize = 20): Promise<PaginatedResponse<JobApplication>> {
    this.logger.debug({ tenantId: tenant.id, page, pageSize }, 'Getting paginated list of job applications');
    return this.request<PaginatedResponse<JobApplication>>(tenant, 'GET', '/job-applications', undefined, {
      params: { page, pageSize },
    });
  }

  /**
   * Get job application by ID
   */
  async getJobApplicationById(tenant: TenantConfig, id: string): Promise<JobApplication> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Getting job application by ID');
    return this.request<JobApplication>(tenant, 'GET', `/job-applications/${id}`);
  }

  /**
   * Create a new job application
   */
  async createJobApplication(tenant: TenantConfig, data: CreateJobApplicationInput): Promise<JobApplication> {
    this.logger.debug({ tenantId: tenant.id, data }, 'Creating new job application');
    return this.request<JobApplication>(tenant, 'POST', '/job-applications', data);
  }

  /**
   * Update an existing job application
   */
  async updateJobApplication(
    tenant: TenantConfig,
    id: string,
    data: UpdateJobApplicationInput,
  ): Promise<JobApplication> {
    this.logger.debug({ tenantId: tenant.id, id, data }, 'Updating job application');
    return this.request<JobApplication>(tenant, 'PUT', `/job-applications/${id}`, data);
  }

  // JobApplication API methods

  /**
   * Get a status by reference key and value
   */
  async getStatusByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Status> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting status by reference key');
    return this.request<Status>(tenant, 'GET', '/statuses/reference', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  /**
   * Get statuses by reference key and value
   */
  async getStatusesByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<Status[]> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting statuses by reference key');
    return this.request<Status[]>(tenant, 'GET', '/statuses/references', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  /**
   * Get a contact by reference key and value
   */
  async getContactByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Contact> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting contact by reference key');
    return this.request<Contact>(tenant, 'GET', '/contacts/reference', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  /**
   * Get contacts by reference key and value
   */
  async getContactsByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<Contact[]> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting contacts by reference key');
    return this.request<Contact[]>(tenant, 'GET', '/contacts/references', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  // Additional reference key methods

  /**
   * Get an account by reference key and value
   */
  async getAccountByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Account> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting account by reference key');
    return this.request<Account>(tenant, 'GET', '/accounts/reference', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  /**
   * Get accounts by reference key and value
   */
  async getAccountsByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<Account[]> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting accounts by reference key');
    return this.request<Account[]>(tenant, 'GET', '/accounts/references', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  /**
   * Get a project by reference key and value
   */
  async getProjectByReferenceKey(tenant: TenantConfig, referenceKey: string, referenceValue: string): Promise<Project> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting project by reference key');
    return this.request<Project>(tenant, 'GET', '/projects/reference', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  /**
   * Get projects by reference key and value
   */
  async getProjectsByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<Project[]> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting projects by reference key');
    return this.request<Project[]>(tenant, 'GET', '/projects/references', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  /**
   * Get a candidate by reference key and value
   */
  async getCandidateByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<Candidate> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting candidate by reference key');
    return this.request<Candidate>(tenant, 'GET', '/candidates/reference', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  /**
   * Get candidates by reference key and value
   */
  async getCandidatesByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<Candidate[]> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting candidates by reference key');
    return this.request<Candidate[]>(tenant, 'GET', '/candidates/references', undefined, {
      params: { key: referenceKey, value: referenceValue },
    });
  }

  /**
   * Get a job application by reference key and value
   */
  async getJobApplicationByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<JobApplication> {
    this.logger.debug(
      { tenantId: tenant.id, referenceKey, referenceValue },
      'Getting job application by reference key',
    );
    const encodedReferenceValue = encodeURIComponent(referenceValue);
    return this.request<JobApplication>(
      tenant,
      'GET',
      `/job-applications/reference/${referenceKey}/${encodedReferenceValue}`,
    );
  }

  /**
   * Get job applications by reference key and value
   */
  async getJobApplicationsByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<JobApplication[]> {
    this.logger.debug(
      { tenantId: tenant.id, referenceKey, referenceValue },
      'Getting job applications by reference key',
    );
    const encodedReferenceValue = encodeURIComponent(referenceValue);
    return this.request<JobApplication[]>(
      tenant,
      'GET',
      `/job-applications/reference/${referenceKey}/${encodedReferenceValue}`,
    );
  }

  /**
   * Get a document by reference key and value
   */
  async getDocumentByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
  ): Promise<Document> {
    this.logger.debug({ tenantId: tenant.id, referenceKey, referenceValue }, 'Getting document by reference key');
    const encodedReferenceValue = encodeURIComponent(referenceValue);
    return this.request<Document>(tenant, 'GET', `/documents/reference/${referenceKey}/${encodedReferenceValue}`);
  }

  /**
   * Get documents by reference key and value
   */
  async getDocumentsByReferenceKey(
    tenant: TenantConfig,
    referenceKey: string,
    referenceValue: string,
    page = 1,
    pageSize = 20,
  ): Promise<PaginatedResponse<Document>> {
    this.logger.debug(
      { tenantId: tenant.id, referenceKey, referenceValue, page, pageSize },
      'Getting documents by reference key',
    );
    const encodedReferenceValue = encodeURIComponent(referenceValue);
    return this.request<PaginatedResponse<Document>>(
      tenant,
      'GET',
      `/documents/reference/${referenceKey}/${encodedReferenceValue}/all`,
      {
        params: { page, pageSize },
      },
    );
  }

  /**
   * Delete a job application
   */
  async deleteJobApplication(tenant: TenantConfig, id: string): Promise<void> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Deleting job application');
    await this.request(tenant, 'DELETE', `/job-applications/${id}`);
  }

  /**
   * Get document by ID
   */
  async getDocumentById(tenant: TenantConfig, id: string): Promise<Document> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Getting document by ID');
    return this.request<Document>(tenant, 'GET', `/documents/${id}`);
  }

  async createDocument(tenant: TenantConfig, input: CreateDocumentInput): Promise<Document> {
    this.logger.debug({ tenantId: tenant.id }, 'Creating document');
    return this.request<Document>(tenant, 'POST', '/documents', input);
  }

  async updateDocument(tenant: TenantConfig, id: string, input: Partial<UpdateDocumentInput>): Promise<Document> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Updating document');
    return this.request<Document>(tenant, 'PUT', `/documents/${id}`, input);
  }

  async deleteDocument(tenant: TenantConfig, id: string): Promise<void> {
    this.logger.debug({ tenantId: tenant.id, id }, 'Deleting document');
    return this.request(tenant, 'DELETE', `/documents/${id}`);
  }

  /**
   * Get or create an axios instance for a tenant
   */
  private getAxiosInstance(tenant: TenantConfig): AxiosInstance {
    if (!tenant?.id || !tenant?.portal?.uri) {
      this.logger.error({ tenantId: tenant?.id }, 'Invalid tenant configuration for Portal V4 API');
      throw new InternalServerErrorException('Invalid tenant configuration for Portal V4 API');
    }

    // Get API key directly from tenant config
    const apiKey = tenant.portal.apiKey || '';

    // Use fixed timeout value
    const timeout = 30000;

    // Use tenant ID as the key for axios instance cache
    if (!this.axiosInstances[tenant.id]) {
      const correlationId = this.cls.get('correlationId');

      // Create new axios instance with tenant-specific configuration
      this.axiosInstances[tenant.id] = axios.create({
        baseURL: `${tenant.portal.uri}/integration/v1`,
        headers: {
          'x-api-key': apiKey,
          'Content-Type': 'application/json',
          ...(correlationId ? { 'x-correlation-id': correlationId } : {}),
        },
        timeout: timeout,
      });

      this.logger.log(
        `Created Portal V4 API axios instance for tenant ${tenant.id} with base URL: ${tenant.portal.uri}`,
      );
    }

    return this.axiosInstances[tenant.id];
  }

  // Document API methods

  /**
   * Creates a request configuration with additional options
   */
  private createRequestConfig(additionalConfig: AxiosRequestConfig = {}): AxiosRequestConfig {
    return {
      ...additionalConfig,
    };
  }

  /**
   * Handle API errors and convert them to NestJS exceptions
   */
  private handleApiError(error: AxiosError): never {
    const status = error.response?.status || 500;
    const message = error.response?.data
      ? (error.response.data as any).message
      : error.message || 'Unknown error occurred';

    this.logger.error({ err: error, status, message }, 'API error occurred');
    throw new HttpException(message, status);
  }

  /**
   * Generic method to make API requests using tenant-specific axios instance
   */
  private async request<T>(
    tenant: TenantConfig,
    method: string,
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const axiosInstance = this.getAxiosInstance(tenant);
    const requestConfig = this.createRequestConfig(config);

    try {
      let response: AxiosResponse<T>;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await axiosInstance.get<T>(endpoint, requestConfig);
          break;
        case 'POST':
          response = await axiosInstance.post<T>(endpoint, data, requestConfig);
          break;
        case 'PUT':
          response = await axiosInstance.put<T>(endpoint, data, requestConfig);
          break;
        case 'PATCH':
          response = await axiosInstance.patch<T>(endpoint, data, requestConfig);
          break;
        case 'DELETE':
          response = await axiosInstance.delete<T>(endpoint, requestConfig);
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.handleApiError(error);
      }
      throw error;
    }
  }
}
