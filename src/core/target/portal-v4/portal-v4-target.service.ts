import { Inject, Injectable } from '@nestjs/common';
import { Target, TargetMeta } from '@core/sync/target.interface';
import { Candidate, Job } from '@core/sync/model';
import { Source } from '@core/sync/source.interface';
import { ConfigService } from '@nestjs/config';
import { EnvConfig, TenantConfig } from '@core/env.model';
import { IPortalV4Service, PORTAL_V4_SERVICE } from '@core/target/portal-v4/portal-v4.service.interface';
import { SOURCE_PROPERTY_RESOLVERS } from '@core/source/source.module';
import { PropertyResolver, PropertyResolverType } from '@core/target/property-resolver.interface';
import { PortalCandidatePropertyResolver } from '@core/target/portal/candidate/portal-candidate-prop-resolver.interface';
import {
  Candidate as PortalCandidate,
  CreateCandidateInput,
  CreateJobApplicationInput,
  UpdateCandidateInput,
  UpdateJobApplicationInput,
  JobApplication as PortalJobApplication,
} from '@core/target/portal-v4/portal-v4.types';
import { EnhancedLoggerService } from '@infrastructure/logger/enhanced-logger.service';
import { ReferenceBuilder } from '@core/target/portal/reference.builder';
import { MimeTypeResolverService } from '@core/mime-type-resolver.service';

export const PORTAL_V4_TARGET_SERVICE = 'PORTAL_V4_TARGET_SERVICE';

export const PORTAL_SOURCE_META_NAME = 'portalV4';

@Injectable()
export class PortalV4TargetServiceImpl implements Target {
  constructor(
    @Inject(PORTAL_V4_SERVICE) private readonly portalService: IPortalV4Service,
    @Inject(SOURCE_PROPERTY_RESOLVERS)
    private propertyResolvers: PropertyResolver[],
    private readonly config: ConfigService<EnvConfig>,
    private readonly logger: EnhancedLoggerService,
    private readonly mimeTypeResolverService: MimeTypeResolverService,
  ) {
    this.logger.setContext(PortalV4TargetServiceImpl.name);
  }

  isConfigured(): boolean {
    return this.config.get<Array<TenantConfig>>('tenants').some(c => c.portal?.uri && c.portal?.apiKey);
  }

  getTargetMeta(): TargetMeta {
    return new TargetMeta(
      PORTAL_SOURCE_META_NAME,
      this.config
        .get<Array<TenantConfig>>('tenants')
        .map(t => t.portal?.uri)
        .join(','),
    );
  }

  async initWithTargetJob(job: Job, source: Source, tenant: TenantConfig): Promise<void> {
    // needed only for sync process
    // job.target = await this.jobService.findByReferenceKey(source, job.id, tenant);
  }

  async importJob(job: Job, source: Source, tenant: TenantConfig): Promise<void> {
    // NOT NEEDED FOR V4
    /*
    const existingJob: portal.Job = await this.jobService.findByReferenceKey(source, job.id, tenant);
    if (existingJob) {
      job.target = existingJob;
      return await this.jobService.updateJob(job, source, tenant);
    }
    await this.jobService.createJob(job, source, tenant);
    */
  }

  async deleteJob(job: Job, source: Source, tenant: TenantConfig): Promise<any> {
    // NOT NEEDED FOR V4
    // await this.jobService.deleteJob(job, source, tenant);
  }

  async importCandidate(
    candidate: Candidate,
    job: Job,
    source: Source,
    tenant: TenantConfig,
    options?: Record<string, any>,
  ): Promise<void> {
    const propResolver = this.findPropertyResolver(source);

    const portalJobId = options.portalProjectId;
    if (!portalJobId) {
      throw new Error('Portal job id is required');
    }

    const scoreParticipationId = options.scoreParticipationId;
    if (!scoreParticipationId) {
      throw new Error('Score participation id is required');
    }

    const candidateEmail = propResolver.getEmail(candidate.source);
    if (!candidateEmail) {
      throw new Error('Candidate email is required');
    }

    const sovren = options.sovren;

    // Create or update candidate
    const existingCandidate = await this.upsertCandidate(tenant, candidate, propResolver, candidateEmail);

    // Create or update job application
    const jobAppRef = ReferenceBuilder.jobApplication(source, propResolver.getId(candidate.source));
    jobAppRef.addReference('score_id', scoreParticipationId);
    jobAppRef.addReference('moonwalk_revision', propResolver.getRevision(candidate.source));

    const existingJobApplication = await this.upsertJobApplication(
      tenant,
      existingCandidate,
      portalJobId,
      jobAppRef,
      propResolver,
      candidate,
      sovren,
    );

    // Create or update documents and link them to job application
    await this.upsertDocuments(
      tenant,
      source,
      job,
      candidate,
      propResolver,
      existingJobApplication,
      scoreParticipationId,
    );
  }

  /**
   * Creates or updates a candidate in the portal
   */
  private async upsertCandidate(
    tenant: TenantConfig,
    candidate: Candidate,
    propResolver: PropertyResolver,
    candidateEmail: string,
  ): Promise<PortalCandidate> {
    const existingCandidatesForEmail = await this.portalService.getCandidates(tenant, 1, 1, {
      email: candidateEmail,
    });

    if (existingCandidatesForEmail.data.length > 1) {
      this.logger.warn(`Multiple candidates found for email ${candidateEmail}`);
    }

    // Check if candidate exists
    let existingCandidate: PortalCandidate;
    if (existingCandidatesForEmail.data.length >= 1) {
      existingCandidate = existingCandidatesForEmail.data[0];
    }

    // Common candidate data
    const candidateData = {
      firstName: propResolver.getFirstName(candidate.source),
      lastName: propResolver.getLastName(candidate.source),
      salutation: propResolver.getSalutation(candidate.source),
      birthday: propResolver.getDateOfBirth(candidate.source),
      phone: propResolver.getCandidateAdditionalData(candidate.source).phone as string,
      residenceCity: propResolver.getCity(candidate.source),
      residencePostCode: isNaN(parseInt(propResolver.getZipCode(candidate.source), 10))
        ? undefined
        : parseInt(propResolver.getZipCode(candidate.source), 10),
    };

    if (existingCandidate) {
      // Update existing candidate
      const updateCandidateInput: UpdateCandidateInput = {
        ...candidateData,
        additionalData: {
          ...(existingCandidate.additionalData || {}),
          score: propResolver.getCandidateContact(candidate.source),
        },
      };
      return await this.portalService.updateCandidate(tenant, existingCandidate.id, updateCandidateInput);
    } else {
      // Create new candidate
      const createCandidateInput: CreateCandidateInput = {
        email: candidateEmail,
        ...candidateData,
        additionalData: {
          score: propResolver.getCandidateContact(candidate.source),
        },
      };
      return await this.portalService.createCandidate(tenant, createCandidateInput);
    }
  }

  /**
   * Creates or updates a job application in the portal
   */
  private async upsertJobApplication(
    tenant: TenantConfig,
    candidate: PortalCandidate,
    portalJobId: string,
    jobAppRef: ReferenceBuilder,
    propResolver: PropertyResolver,
    originalCandidate: Candidate,
    sovren: any,
  ): Promise<PortalJobApplication> {
    // Check if job application exists
    const existingJobApplication = await this.portalService.getJobApplicationByReferenceKey(
      tenant,
      jobAppRef.key,
      jobAppRef.value,
    );

    if (existingJobApplication) {
      // Update existing job application
      const updateJobApplicationInput: UpdateJobApplicationInput = {
        reference: {
          ...(existingJobApplication.reference || {}),
          ...jobAppRef.toRecord(),
        },
        additionalData: {
          ...(existingJobApplication.additionalData || {}),
          score: {
            softfactorsData: propResolver.getSoftfactorsData(originalCandidate.source),
            sovren: sovren,
          },
        },
      };
      return await this.portalService.updateJobApplication(
        tenant,
        existingJobApplication.id,
        updateJobApplicationInput,
      );
    } else {
      // Create new job application
      const createJobApplicationInput: CreateJobApplicationInput = {
        active: true,
        candidateId: candidate.id,
        projectId: portalJobId,
        status: 'TODO set this with mapping for BI',
        additionalData: {
          score: {
            softfactorsData: propResolver.getSoftfactorsData(originalCandidate.source),
            sovren: sovren,
          },
        },
        reference: jobAppRef.toRecord(),
      };
      return await this.portalService.createJobApplication(tenant, createJobApplicationInput);
    }
  }

  /**
   * Creates or updates documents and links them to the job application
   */
  private async upsertDocuments(
    tenant: TenantConfig,
    source: Source,
    job: Job,
    candidate: Candidate,
    propResolver: PropertyResolver,
    jobApplication: PortalJobApplication,
    scoreParticipationId: string,
  ): Promise<void> {
    const documents = propResolver.getAttachments(candidate.source);
    const savedDocuments: string[] = [];

    for (const document of documents) {
      const documentBuffer = await source.getDocument(job.id, document.id, tenant, candidate.id);

      const documentRef = ReferenceBuilder.document(source, document.id);
      documentRef.addReference('scoreJobApplicationId', scoreParticipationId);
      documentRef.addReference('moonwalkParticipationId', propResolver.getId(candidate.source));
      documentRef.addReference('moonwalk_revision', propResolver.getRevision(candidate.source));

      let existingDocument = await this.portalService.getDocumentByReferenceKey(
        tenant,
        documentRef.key,
        documentRef.value,
      );

      // Common document data
      const documentData = {
        title: document.fileName,
        documentType: document.type,
        mimeType: this.mimeTypeResolverService.resolve(document.fileName),
        data: documentBuffer.toString('base64'),
        byteSize: documentBuffer.length,
        reference: documentRef.toRecord(),
      };

      if (existingDocument) {
        existingDocument = await this.portalService.updateDocument(tenant, existingDocument.id, documentData);
      } else {
        existingDocument = await this.portalService.createDocument(tenant, documentData);
      }

      savedDocuments.push(existingDocument.id);
    }

    // Get existing document references from job application
    const existingDocumentReferences = jobApplication.documentReferences || [];

    // Combine existing and new document references, ensuring no duplicates
    const updatedDocumentReferences = [
      ...existingDocumentReferences,
      ...savedDocuments.filter(docId => !existingDocumentReferences.includes(docId)),
    ];

    await this.portalService.updateJobApplication(tenant, jobApplication.id, {
      documentReferences: updatedDocumentReferences,
    });
  }

  async deleteCandidate(candidate: Candidate, source: Source, tenant: TenantConfig): Promise<void> {
    // await this.candidateService.deleteCandidate(candidate, source, tenant);
  }

  private findPropertyResolver(source: Source): PortalCandidatePropertyResolver {
    return this.propertyResolvers.find(resolver =>
      resolver.canResolve(source, PropertyResolverType.CANDIDATE),
    ) as PortalCandidatePropertyResolver;
  }
}
