/**
 * Portal V4 API Types
 * Types for EmmySoft Integration API
 */

// Status types
export interface Status {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  pathGroup: string;
  rank: number;
  type?: 'APPLIED' | 'REJECTED' | 'HIRED' | null;
  isEvent: boolean;
  isPresented: boolean;
  candidateFieldSet?: string | null;
  jobApplicationFieldSet?: string | null;
  stepFieldSet?: string | null;
  nameLocalized?: Record<string, string> | null;
  reference?: Record<string, string> | null;
}

export interface CreateStatusInput {
  name: string;
  description?: string;
  pathGroup: string;
  rank: number;
  type?: 'APPLIED' | 'REJECTED' | 'HIRED' | null;
  isEvent?: boolean;
  isPresented?: boolean;
  nameLocalized?: Record<string, string> | null;
  reference?: Record<string, string> | null;
}

export interface UpdateStatusInput {
  name?: string;
  description?: string;
}

// Contact types
export interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email?: string | null;
  phone?: string;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
  createdById: string;
  createdDate: string;
  lastModifiedById: string;
  lastModifiedDate: string;
  name: string;
  accountId: string;
  assistantName?: string | null;
  assistantPhone?: string | null;
  birthdate?: string | null;
  department?: string | null;
  description?: string | null;
  emmyFotoUrl?: string | null;
  fax?: string | null;
  homePhone?: string | null;
  leadSource?: string | null;
  mailingAddress?: any | null;
  mailingCity?: string | null;
  mailingCountry?: string | null;
  mailingGeocodeAccuracy?: string | null;
  mailingLatitude?: number | null;
  mailingLongitude?: number | null;
  mailingPostalCode?: string | null;
  mailingState?: string | null;
  mailingStreet?: string | null;
  masterRecordId?: string | null;
  mobilePhone?: string | null;
  otherAddress?: any | null;
  otherCity?: string | null;
  otherCountry?: string | null;
  otherGeocodeAccuracy?: string | null;
  otherLatitude?: number | null;
  otherLongitude?: number | null;
  otherPhone?: string | null;
  otherPostalCode?: string | null;
  otherState?: string | null;
  otherStreet?: string | null;
  reference?: Record<string, any> | null;
  additionalData?: Record<string, any> | null;
}

export interface CreateContactInput {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
}

export interface UpdateContactInput {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
}

export interface CreateContactRequest {
  lastName: string;
  firstName: string;
  accountId: string;
}

export interface UpdateContactRequest {
  lastName?: string;
  firstName?: string;
  accountId?: string;
}

// Account types
export interface Account {
  id: string;
  name: string;
  type?: string;
  status?: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  createdDate: string;
  lastModifiedDate: string;
  createdById: string;
  lastModifiedById: string;
  accountSource?: string | null;
  annualRevenue?: number | null;
  billingAddress?: string | null;
  billingCity?: string | null;
  billingCountry?: string | null;
  billingGeocodeAccuracy?: string | null;
  billingLatitude?: number | null;
  billingLongitude?: number | null;
  billingPostalCode?: string | null;
  billingState?: string | null;
  billingStreet?: string | null;
  description?: string | null;
  dfindAlliance?: string | null;
  dfindCategory?: string | null;
  dfindCompany?: string | null;
  dfindCounty?: string | null;
  dfindGroup?: string | null;
  dfindGroupCriteria?: string | null;
  dfindLocations?: number | null;
  dfindMAClass?: string | null;
  dfindUnterkategorie?: string | null;
  euCode?: string | null;
  fax?: string | null;
  holding?: string | null;
  industry?: string | null;
  numberOfEmployees?: number | null;
  ownerId: string;
  parentId?: string | null;
  phone?: string | null;
  sektor?: string | null;
  shippingAddress?: string | null;
  shippingCity?: string | null;
  shippingCountry?: string | null;
  shippingGeocodeAccuracy?: string | null;
  shippingLatitude?: number | null;
  shippingLongitude?: number | null;
  shippingPostalCode?: string | null;
  shippingState?: string | null;
  shippingStreet?: string | null;
  website?: string | null;
  reference?: Record<string, string> | null;
}

export interface CreateAccountInput {
  name: string;
  type: 'personal' | 'business';
}

export interface UpdateAccountInput {
  name?: string;
  status?: 'active' | 'inactive';
}

export interface AccountInput {
  name: string;
  accountSource?: string | null;
  annualRevenue?: number | null;
  billingAddress?: string | null;
  billingCity?: string | null;
  billingCountry?: string | null;
  billingGeocodeAccuracy?: string | null;
  billingLatitude?: number | null;
  billingLongitude?: number | null;
  billingPostalCode?: string | null;
  billingState?: string | null;
  billingStreet?: string | null;
  description?: string | null;
  dfindAlliance?: string | null;
  dfindCategory?: string | null;
  dfindCompany?: string | null;
  dfindCounty?: string | null;
  dfindGroup?: string | null;
  dfindGroupCriteria?: string | null;
  dfindLocations?: number | null;
  dfindMAClass?: string | null;
  dfindUnterkategorie?: string | null;
  euCode?: string | null;
  fax?: string | null;
  holding?: string | null;
  industry?: string | null;
  numberOfEmployees?: number | null;
  ownerId: string;
  parentId?: string | null;
  phone?: string | null;
  sektor?: string | null;
  shippingAddress?: string | null;
  shippingCity?: string | null;
  shippingCountry?: string | null;
  shippingGeocodeAccuracy?: string | null;
  shippingLatitude?: number | null;
  shippingLongitude?: number | null;
  shippingPostalCode?: string | null;
  shippingState?: string | null;
  shippingStreet?: string | null;
  type?: string | null;
  website?: string | null;
  reference?: Record<string, string> | null;
}

// Project types
export interface Project {
  id: string;
  name: string;
  description?: string;
  status?: 'active' | 'completed' | 'on-hold' | 'cancelled' | null;
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
  nameLocalized?: Record<string, any> | null;
  accountId?: string | null;
  positionName?: string | null;
  actionRequired: boolean;
  jobStartDay?: string | null;
  pathCommunity?: string | null;
  workplace?: string | null;
  salaryFrom?: number | null;
  salaryTo?: number | null;
  jobDescription?: string | null;
  jobDescriptionLocalized?: Record<string, any> | null;
  departmentProfile?: string | null;
  koQuestions?: string | null;
  contactDepartmentId?: string | null;
  contactHiringManagerId?: string | null;
  contactHRAdvisorId?: string | null;
  contactdfindId?: string | null;
  contactdfind2Id?: string | null;
  contactdfind3Id?: string | null;
  documentReferences?: string[] | null;
  shareOnPortal?: boolean | null;
  reference?: Record<string, any> | null;
  additionalData?: Record<string, any> | null;
}

export interface CreateProjectInput {
  name: string;
  description?: string;
  status?: 'active' | 'completed' | 'on-hold' | 'cancelled';
  startDate?: string;
  endDate?: string;
}

export interface UpdateProjectInput {
  name?: string;
  description?: string;
  status?: 'active' | 'completed' | 'on-hold' | 'cancelled';
  startDate?: string;
  endDate?: string;
}

export interface CreateProject {
  name: string;
  nameLocalized?: Record<string, any> | null;
  accountId?: string | null;
  positionName?: string | null;
  actionRequired?: boolean;
  jobStartDay?: string | null;
  pathCommunity?: string | null;
  workplace?: string | null;
  status?: string | null;
  salaryFrom?: number | null;
  salaryTo?: number | null;
  jobDescription?: string | null;
  jobDescriptionLocalized?: Record<string, any> | null;
  departmentProfile?: string | null;
  koQuestions?: string | null;
  contactDepartmentId?: string | null;
  contactHiringManagerId?: string | null;
  contactHRAdvisorId?: string | null;
  contactdfindId?: string | null;
  contactdfind2Id?: string | null;
  contactdfind3Id?: string | null;
  documentReferences?: string[] | null;
  shareOnPortal?: boolean | null;
  reference?: Record<string, any> | null;
  additionalData?: Record<string, any> | null;
}

// Candidate types
export interface Candidate {
  id: string;
  createdDate: Date;
  createdById: string;
  lastModifiedDate: Date;
  lastModifiedById: string;

  // Optional string fields
  firstName?: string | null;
  lastName?: string | null;
  vCard?: string | null;
  phone?: string | null;
  email?: string | null;
  linkedin?: string | null;
  xing?: string | null;
  salutation?: string | null;
  title?: string | null;
  university?: string | null;
  universityDegree?: string | null;
  willingnessToTravel?: string | null;
  willingnessToMove?: string | null;
  salaryExpectation?: string | null;
  currentSalary?: string | null;
  bestStatus?: string | null;
  noticePeriod?: string | null;
  residenceCity?: string | null;
  employer?: string | null;
  status?: 'active' | 'inactive';

  // Date fields
  gdprDate?: Date | null;
  bestStatusDate?: Date | null;
  birthday?: Date | null;

  // Boolean fields
  gdpr?: boolean | null;

  // Number fields
  salary?: number | null;
  residencePostCode?: number | null;
  remoteWork?: number | null;

  // Text fields
  prequalificationNotes?: string | null;
  languageSkills?: string | null;
  changeMotivation?: string | null;
  personalityLeadership?: string | null;
  workExperiense?: string | null;
  branchenKnowHow?: string | null;

  // Array fields
  documentReferences?: string[] | null;

  // Reference fields
  employerId?: string | null;
  reference?: Record<string, any> | null;
  additionalData?: Record<string, any> | null;

  // Legacy fields (keeping for backward compatibility)
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateCandidateInput {
  // Optional string fields with max length
  firstName: string;
  lastName: string;
  vCard?: string;
  phone?: string;
  email: string;
  linkedin?: string;
  xing?: string;
  salutation?: string;
  title?: string;
  university?: string;
  universityDegree?: string;
  willingnessToTravel?: string;
  willingnessToMove?: string;
  salaryExpectation?: string;
  currentSalary?: string;
  bestStatus?: string;
  noticePeriod?: string;
  residenceCity?: string;
  employer?: string;

  // Date fields
  gdprDate?: Date;
  bestStatusDate?: Date;
  birthday?: Date;

  // Boolean fields
  gdpr?: boolean;

  // Number fields
  salary?: number;
  residencePostCode?: number;
  remoteWork?: number;

  // Text fields without length restriction
  prequalificationNotes?: string;
  languageSkills?: string;
  changeMotivation?: string;
  personalityLeadership?: string;
  workExperiense?: string;
  branchenKnowHow?: string;

  // Array fields
  documentReferences?: string[];

  // Reference fields
  employerId?: string;
  reference?: Record<string, any>;
  additionalData?: Record<string, any>;
}

export interface UpdateCandidateInput {
  // Optional string fields with max length
  firstName?: string;
  lastName?: string;
  vCard?: string;
  phone?: string;
  email?: string;
  linkedin?: string;
  xing?: string;
  salutation?: string;
  title?: string;
  university?: string;
  universityDegree?: string;
  willingnessToTravel?: string;
  willingnessToMove?: string;
  salaryExpectation?: string;
  currentSalary?: string;
  bestStatus?: string;
  noticePeriod?: string;
  residenceCity?: string;
  employer?: string;
  status?: 'active' | 'inactive';

  // Date fields
  gdprDate?: Date;
  bestStatusDate?: Date;
  birthday?: Date;

  // Boolean fields
  gdpr?: boolean;

  // Number fields
  salary?: number;
  residencePostCode?: number;
  remoteWork?: number;

  // Text fields without length restriction
  prequalificationNotes?: string;
  languageSkills?: string;
  changeMotivation?: string;
  personalityLeadership?: string;
  workExperiense?: string;
  branchenKnowHow?: string;

  // Array fields
  documentReferences?: string[];

  // Reference fields
  employerId?: string;
  reference?: Record<string, any>;
  additionalData?: Record<string, any>;
}

// JobApplication types
export interface JobApplication {
  id: string;
  name?: string | null;
  active: boolean;
  candidateId?: string | null;
  projectId?: string | null;
  status?: string | null;
  rejectionStatus?: string | null;
  ratingPoints?: number | null;
  counterSteps?: number | null;
  nextInterviewDate?: Date | null;
  answerToKoQuestions?: string | null;
  changeMotivation?: string | null;
  personalityLeadership?: string | null;
  workExperiense?: string | null;
  rejectionNotes?: string | null;
  rejectionBy?: string | null;
  rejectionReason?: string | null;
  rejectionReasonSpecified?: string | null;
  shareOnCommunity?: boolean | null;
  documentReferences?: string[] | null;
  salaryExpectation?: string | null;
  reference?: Record<string, any> | null;
  additionalData?: Record<string, any> | null;
  appliedAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateJobApplicationInput {
  name?: string | null;
  active: boolean;
  candidateId?: string | null;
  projectId?: string | null;
  status?: string | null;
  rejectionStatus?: string | null;
  ratingPoints?: number | null;
  counterSteps?: number | null;
  nextInterviewDate?: Date | null;
  answerToKoQuestions?: string | null;
  changeMotivation?: string | null;
  personalityLeadership?: string | null;
  workExperiense?: string | null;
  rejectionNotes?: string | null;
  rejectionBy?: string | null;
  rejectionReason?: string | null;
  rejectionReasonSpecified?: string | null;
  shareOnCommunity?: boolean | null;
  documentReferences?: string[] | null;
  salaryExpectation?: string | null;
  reference?: Record<string, any> | null;
  additionalData?: Record<string, any> | null;
}

export interface UpdateJobApplicationInput {
  name?: string | null;
  active?: boolean;
  candidateId?: string | null;
  projectId?: string | null;
  status?: string | null;
  rejectionStatus?: string | null;
  ratingPoints?: number | null;
  counterSteps?: number | null;
  nextInterviewDate?: Date | null;
  answerToKoQuestions?: string | null;
  changeMotivation?: string | null;
  personalityLeadership?: string | null;
  workExperiense?: string | null;
  rejectionNotes?: string | null;
  rejectionBy?: string | null;
  rejectionReason?: string | null;
  rejectionReasonSpecified?: string | null;
  shareOnCommunity?: boolean | null;
  documentReferences?: string[] | null;
  salaryExpectation?: string | null;
  reference?: Record<string, any> | null;
  additionalData?: Record<string, any> | null;
}

// Document types
export interface Document {
  id: string;
  title: string;
  documentType: 'photo' | 'cv' | 'other';
  timestamp: Date;
  mimeType: string;
  data: string;
  byteSize: number;
  xfdf: string;
  reference?: Record<string, any> | null;
}

export interface CreateDocumentInput {
  title: string;
  documentType: 'photo' | 'cv' | 'other';
  mimeType: string;
  data: string;
  byteSize?: number;
  xfdf?: string;
  reference?: Record<string, any> | null;
}

export interface UpdateDocumentInput {
  title: string;
  documentType: string;
  mimeType: string;
  data: string;
  byteSize?: number;
  xfdf?: string;
  reference?: Record<string, any> | null;
}

// Pagination and error types
export interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationInfo;
}

export interface ErrorResponse {
  code: number;
  message: string;
}

// API Configuration
export interface PortalV4Config {
  baseUrl: string;
  apiKey: string;
  timeout?: number;
}
