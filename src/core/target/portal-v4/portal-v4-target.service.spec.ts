import { Test, TestingModule } from '@nestjs/testing';
import { createMock, DeepMocked } from '@golevelup/ts-jest';
import { ConfigService } from '@nestjs/config';
import { PortalV4TargetServiceImpl } from './portal-v4-target.service';
import { IPortalV4Service, PORTAL_V4_SERVICE } from './portal-v4.service.interface';
import { SOURCE_PROPERTY_RESOLVERS } from '@core/source/source.module';
import { EnhancedLoggerService } from '@infrastructure/logger/enhanced-logger.service';
import { MimeTypeResolverService } from '@core/mime-type-resolver.service';
import { PortalCandidatePropertyResolver } from '@core/target/portal/candidate/portal-candidate-prop-resolver.interface';
import { Candidate, Job } from '@core/sync/model';
import { Source } from '@core/sync/source.interface';
import { EnvConfig, TenantConfig } from '@core/env.model';
import {
  Candidate as PortalCandidate,
  Document as PortalDocument,
  JobApplication as PortalJobApplication,
  PaginatedResponse,
} from './portal-v4.types';
import { ReferenceBuilder } from '@core/target/portal/reference.builder';

// Mock ReferenceBuilder
jest.mock('@core/target/portal/reference.builder');

describe('PortalV4TargetServiceImpl - importCandidate', () => {
  let service: PortalV4TargetServiceImpl;
  let portalService: DeepMocked<IPortalV4Service>;
  let configService: DeepMocked<ConfigService<EnvConfig>>;
  let logger: DeepMocked<EnhancedLoggerService>;
  let mimeTypeResolver: DeepMocked<MimeTypeResolverService>;
  let propertyResolver: DeepMocked<PortalCandidatePropertyResolver>;
  let source: DeepMocked<Source>;

  const mockTenant: TenantConfig = {
    id: 1,
    name: 'test-tenant',
    portal: { uri: 'https://test.portal.com', apiKey: 'test-key' },
  } as TenantConfig;

  const mockCandidate: Candidate = {
    id: 'candidate-123',
    source: { id: 'source-candidate-123' },
  } as Candidate;

  const mockJob: Job = {
    id: 'job-123',
    source: { id: 'source-job-123' },
  } as Job;

  const mockOptions = {
    portalProjectId: 'portal-project-123',
    scoreParticipationId: 'score-participation-123',
    sovren: { parsedResume: true },
  };

  beforeEach(async () => {
    const mockReference = {
      key: 'test-key',
      value: 'test-value',
      addReference: jest.fn(),
      toRecord: jest.fn().mockReturnValue({ 'test-key': 'test-value' }),
    };

    (ReferenceBuilder.jobApplication as jest.Mock) = jest.fn().mockReturnValue(mockReference);
    (ReferenceBuilder.document as jest.Mock) = jest.fn().mockReturnValue(mockReference);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PortalV4TargetServiceImpl,
        {
          provide: PORTAL_V4_SERVICE,
          useValue: createMock<IPortalV4Service>(),
        },
        {
          provide: SOURCE_PROPERTY_RESOLVERS,
          useValue: [createMock<PortalCandidatePropertyResolver>()],
        },
        {
          provide: ConfigService,
          useValue: createMock<ConfigService<EnvConfig>>(),
        },
        {
          provide: EnhancedLoggerService,
          useValue: createMock<EnhancedLoggerService>(),
        },
        {
          provide: MimeTypeResolverService,
          useValue: createMock<MimeTypeResolverService>(),
        },
      ],
    }).compile();

    service = module.get<PortalV4TargetServiceImpl>(PortalV4TargetServiceImpl);
    portalService = module.get(PORTAL_V4_SERVICE);
    configService = module.get(ConfigService);
    logger = module.get(EnhancedLoggerService);
    mimeTypeResolver = module.get(MimeTypeResolverService);

    // Setup property resolver mock
    propertyResolver = createMock<PortalCandidatePropertyResolver>();
    propertyResolver.canResolve.mockReturnValue(true);
    const propertyResolvers = module.get(SOURCE_PROPERTY_RESOLVERS);
    propertyResolvers[0] = propertyResolver;

    // Setup source mock
    source = createMock<Source>();
    source.getSourceMeta.mockReturnValue({ name: 'test-source', uri: 'test-uri' });
  });

  describe('importCandidate', () => {
    beforeEach(() => {
      // Setup common property resolver mocks
      propertyResolver.getEmail.mockReturnValue('<EMAIL>');
      propertyResolver.getId.mockReturnValue('candidate-123');
      propertyResolver.getRevision.mockReturnValue('rev-1');
      propertyResolver.getFirstName.mockReturnValue('John');
      propertyResolver.getLastName.mockReturnValue('Doe');
      propertyResolver.getSalutation.mockReturnValue('Mr.');
      propertyResolver.getDateOfBirth.mockReturnValue(new Date('1990-01-01'));
      propertyResolver.getCandidateAdditionalData.mockReturnValue({ phone: '+1234567890' });
      propertyResolver.getCity.mockReturnValue('Test City');
      propertyResolver.getZipCode.mockReturnValue('12345');
      propertyResolver.getCandidateContact.mockReturnValue({ contact: 'data' });
      propertyResolver.getSoftfactorsData.mockReturnValue({ softfactors: 'data' });
      propertyResolver.getAttachments.mockReturnValue([]);
    });

    it('should throw error when portalProjectId is missing', async () => {
      const optionsWithoutPortalId = { ...mockOptions };
      delete optionsWithoutPortalId.portalProjectId;

      await expect(
        service.importCandidate(mockCandidate, mockJob, source, mockTenant, optionsWithoutPortalId),
      ).rejects.toThrow('Portal job id is required');
    });

    it('should throw error when scoreParticipationId is missing', async () => {
      const optionsWithoutScoreId = { ...mockOptions };
      delete optionsWithoutScoreId.scoreParticipationId;

      await expect(
        service.importCandidate(mockCandidate, mockJob, source, mockTenant, optionsWithoutScoreId),
      ).rejects.toThrow('Score participation id is required');
    });

    it('should throw error when candidate email is missing', async () => {
      propertyResolver.getEmail.mockReturnValue(null);

      await expect(service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions)).rejects.toThrow(
        'Candidate email is required',
      );
    });

    it('should create new candidate when none exists', async () => {
      const mockPortalCandidate: PortalCandidate = {
        id: 'portal-candidate-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        additionalData: {},
      } as PortalCandidate;

      const mockJobApplication: PortalJobApplication = {
        id: 'job-app-123',
        candidateId: 'portal-candidate-123',
        projectId: 'portal-project-123',
        active: true,
        documentReferences: [],
      } as PortalJobApplication;

      // Mock no existing candidates
      portalService.getCandidates.mockResolvedValue({
        data: [],
        pagination: { page: 1, pageSize: 1, total: 0 },
      } as PaginatedResponse<PortalCandidate>);

      portalService.createCandidate.mockResolvedValue(mockPortalCandidate);
      portalService.getJobApplicationByReferenceKey.mockResolvedValue(null);
      portalService.createJobApplication.mockResolvedValue(mockJobApplication);

      await service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions);

      expect(portalService.getCandidates).toHaveBeenCalledWith(mockTenant, 1, 1, {
        email: '<EMAIL>',
      });
      expect(portalService.createCandidate).toHaveBeenCalledWith(mockTenant, {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        salutation: 'Mr.',
        birthday: new Date('1990-01-01'),
        phone: '+1234567890',
        residenceCity: 'Test City',
        residencePostCode: 12345,
        additionalData: {
          score: { contact: 'data' },
        },
      });
      expect(portalService.createJobApplication).toHaveBeenCalled();
    });

    it('should update existing candidate when found', async () => {
      const existingCandidate: PortalCandidate = {
        id: 'existing-candidate-123',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        additionalData: { existing: 'data' },
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        createdById: 'user-1',
        lastModifiedById: 'user-1',
        lastModifiedDate: new Date(),
        createdDate: new Date(),
      } as PortalCandidate;

      const updatedCandidate: PortalCandidate = {
        ...existingCandidate,
        firstName: 'John',
        lastName: 'Doe',
      };

      const mockJobApplication: PortalJobApplication = {
        id: 'job-app-123',
        candidateId: 'existing-candidate-123',
        projectId: 'portal-project-123',
        active: true,
        documentReferences: [],
      } as PortalJobApplication;

      portalService.getCandidates.mockResolvedValue({
        data: [existingCandidate],
        pagination: { page: 1, pageSize: 1, total: 1 },
      } as PaginatedResponse<PortalCandidate>);

      portalService.updateCandidate.mockResolvedValue(updatedCandidate);
      portalService.getJobApplicationByReferenceKey.mockResolvedValue(null);
      portalService.createJobApplication.mockResolvedValue(mockJobApplication);

      await service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions);

      expect(portalService.updateCandidate).toHaveBeenCalledWith(mockTenant, 'existing-candidate-123', {
        firstName: 'John',
        lastName: 'Doe',
        salutation: 'Mr.',
        birthday: new Date('1990-01-01'),
        phone: '+1234567890',
        residenceCity: 'Test City',
        residencePostCode: 12345,
        additionalData: {
          existing: 'data',
          score: { contact: 'data' },
        },
      });
    });

    it('should warn when multiple candidates found for same email', async () => {
      const candidates: PortalCandidate[] = [
        { id: 'candidate-1', email: '<EMAIL>' } as PortalCandidate,
        { id: 'candidate-2', email: '<EMAIL>' } as PortalCandidate,
      ];

      const mockJobApplication: PortalJobApplication = {
        id: 'job-app-123',
        candidateId: 'candidate-1',
        projectId: 'portal-project-123',
        active: true,
        documentReferences: [],
      } as PortalJobApplication;

      portalService.getCandidates.mockResolvedValue({
        data: candidates,
        pagination: { page: 1, pageSize: 1, total: 2 },
      } as PaginatedResponse<PortalCandidate>);

      portalService.updateCandidate.mockResolvedValue(candidates[0]);
      portalService.getJobApplicationByReferenceKey.mockResolvedValue(null);
      portalService.createJobApplication.mockResolvedValue(mockJobApplication);

      await service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions);

      expect(logger.warn).toHaveBeenCalledWith('Multiple candidates found <NAME_EMAIL>');
    });

    it('should handle invalid zip code gracefully', async () => {
      propertyResolver.getZipCode.mockReturnValue('invalid-zip');

      const mockPortalCandidate: PortalCandidate = {
        id: 'portal-candidate-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        additionalData: {},
      } as PortalCandidate;

      const mockJobApplication: PortalJobApplication = {
        id: 'job-app-123',
        candidateId: 'portal-candidate-123',
        projectId: 'portal-project-123',
        active: true,
        documentReferences: [],
      } as PortalJobApplication;

      portalService.getCandidates.mockResolvedValue({
        data: [],
        pagination: { page: 1, pageSize: 1, total: 0 },
      } as PaginatedResponse<PortalCandidate>);

      portalService.createCandidate.mockResolvedValue(mockPortalCandidate);
      portalService.getJobApplicationByReferenceKey.mockResolvedValue(null);
      portalService.createJobApplication.mockResolvedValue(mockJobApplication);

      await service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions);

      expect(portalService.createCandidate).toHaveBeenCalledWith(
        mockTenant,
        expect.objectContaining({
          residencePostCode: undefined, // Should be undefined for invalid zip
        }),
      );
    });

    it('should create new job application when none exists', async () => {
      const mockPortalCandidate: PortalCandidate = {
        id: 'portal-candidate-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        additionalData: {},
      } as PortalCandidate;

      const mockJobApplication: PortalJobApplication = {
        id: 'job-app-123',
        candidateId: 'portal-candidate-123',
        projectId: 'portal-project-123',
        active: true,
        documentReferences: [],
      } as PortalJobApplication;

      portalService.getCandidates.mockResolvedValue({
        data: [mockPortalCandidate],
        pagination: { page: 1, pageSize: 1, total: 1 },
      } as PaginatedResponse<PortalCandidate>);

      portalService.updateCandidate.mockResolvedValue(mockPortalCandidate);
      portalService.getJobApplicationByReferenceKey.mockResolvedValue(null);
      portalService.createJobApplication.mockResolvedValue(mockJobApplication);

      await service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions);

      expect(portalService.createJobApplication).toHaveBeenCalledWith(mockTenant, {
        active: true,
        candidateId: 'portal-candidate-123',
        projectId: 'portal-project-123',
        status: 'TODO set this with mapping for BI',
        additionalData: {
          score: {
            softfactorsData: { softfactors: 'data' },
            sovren: { parsedResume: true },
          },
        },
        reference: { 'test-key': 'test-value' },
      });
    });

    it('should update existing job application when found', async () => {
      const mockPortalCandidate: PortalCandidate = {
        id: 'portal-candidate-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        additionalData: {},
      } as PortalCandidate;

      const existingJobApplication: PortalJobApplication = {
        id: 'existing-job-app-123',
        candidateId: 'portal-candidate-123',
        projectId: 'portal-project-123',
        active: true,
        reference: { existing: 'reference' },
        additionalData: { existing: 'data' },
        documentReferences: [],
        appliedAt: '2023-01-01T00:00:00Z',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      } as PortalJobApplication;

      const updatedJobApplication: PortalJobApplication = {
        ...existingJobApplication,
        reference: { existing: 'reference', 'test-key': 'test-value' },
        additionalData: {
          existing: 'data',
          score: {
            softfactorsData: { softfactors: 'data' },
            sovren: { parsedResume: true },
          },
        },
      };

      portalService.getCandidates.mockResolvedValue({
        data: [mockPortalCandidate],
        pagination: { page: 1, pageSize: 1, total: 1 },
      } as PaginatedResponse<PortalCandidate>);

      portalService.updateCandidate.mockResolvedValue(mockPortalCandidate);
      portalService.getJobApplicationByReferenceKey.mockResolvedValue(existingJobApplication);
      portalService.updateJobApplication.mockResolvedValue(updatedJobApplication);

      await service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions);

      expect(portalService.updateJobApplication).toHaveBeenCalledWith(mockTenant, 'existing-job-app-123', {
        reference: {
          existing: 'reference',
          'test-key': 'test-value',
        },
        additionalData: {
          existing: 'data',
          score: {
            softfactorsData: { softfactors: 'data' },
            sovren: { parsedResume: true },
          },
        },
      });
    });

    it('should handle document processing correctly', async () => {
      const mockAttachments = [
        {
          id: 'doc-1',
          fileName: 'resume.pdf',
          downloadUrl: 'http://example.com/resume.pdf',
          type: 'cv' as const,
        },
        {
          id: 'doc-2',
          fileName: 'cover-letter.docx',
          downloadUrl: 'http://example.com/cover.docx',
          type: 'other' as const,
        },
      ];

      const mockDocumentBuffer = Buffer.from('mock document content');
      const mockDocument: PortalDocument = {
        id: 'portal-doc-123',
        title: 'resume.pdf',
        documentType: 'cv',
        mimeType: 'application/pdf',
        data: mockDocumentBuffer.toString('base64'),
        byteSize: mockDocumentBuffer.length,
        timestamp: new Date(),
        xfdf: '',
        reference: { 'test-key': 'test-value' },
      };

      const mockPortalCandidate: PortalCandidate = {
        id: 'portal-candidate-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        additionalData: {},
      } as PortalCandidate;

      const mockJobApplication: PortalJobApplication = {
        id: 'job-app-123',
        candidateId: 'portal-candidate-123',
        projectId: 'portal-project-123',
        active: true,
        documentReferences: [],
      } as PortalJobApplication;

      propertyResolver.getAttachments.mockReturnValue(mockAttachments);
      source.getDocument.mockResolvedValue(mockDocumentBuffer);
      mimeTypeResolver.resolve.mockReturnValue('application/pdf');

      portalService.getCandidates.mockResolvedValue({
        data: [mockPortalCandidate],
        pagination: { page: 1, pageSize: 1, total: 1 },
      } as PaginatedResponse<PortalCandidate>);

      portalService.updateCandidate.mockResolvedValue(mockPortalCandidate);
      portalService.getJobApplicationByReferenceKey.mockResolvedValue(null);
      portalService.createJobApplication.mockResolvedValue(mockJobApplication);
      portalService.getDocumentByReferenceKey.mockResolvedValue(null);
      portalService.createDocument.mockResolvedValue(mockDocument);
      portalService.updateJobApplication.mockResolvedValue(mockJobApplication);

      await service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions);

      expect(source.getDocument).toHaveBeenCalledTimes(2);
      expect(portalService.createDocument).toHaveBeenCalledTimes(2);
      expect(portalService.updateJobApplication).toHaveBeenCalledWith(mockTenant, 'job-app-123', {
        documentReferences: ['portal-doc-123', 'portal-doc-123'],
      });
    });

    it('should update existing documents when found', async () => {
      const mockAttachment = {
        id: 'doc-1',
        fileName: 'resume.pdf',
        downloadUrl: 'http://example.com/resume.pdf',
        type: 'cv' as const,
      };

      const mockDocumentBuffer = Buffer.from('updated document content');
      const existingDocument: PortalDocument = {
        id: 'existing-doc-123',
        title: 'old-resume.pdf',
        documentType: 'cv',
        mimeType: 'application/pdf',
        data: 'old-content',
        byteSize: 100,
        timestamp: new Date(),
        xfdf: '',
        reference: { old: 'reference' },
      };

      const updatedDocument: PortalDocument = {
        ...existingDocument,
        title: 'resume.pdf',
        data: mockDocumentBuffer.toString('base64'),
        byteSize: mockDocumentBuffer.length,
        reference: { old: 'reference', 'test-key': 'test-value' },
      };

      const mockPortalCandidate: PortalCandidate = {
        id: 'portal-candidate-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        additionalData: {},
      } as PortalCandidate;

      const mockJobApplication: PortalJobApplication = {
        id: 'job-app-123',
        candidateId: 'portal-candidate-123',
        projectId: 'portal-project-123',
        active: true,
        documentReferences: ['existing-doc-456'],
      } as PortalJobApplication;

      propertyResolver.getAttachments.mockReturnValue([mockAttachment]);
      source.getDocument.mockResolvedValue(mockDocumentBuffer);
      mimeTypeResolver.resolve.mockReturnValue('application/pdf');

      portalService.getCandidates.mockResolvedValue({
        data: [mockPortalCandidate],
        pagination: { page: 1, pageSize: 1, total: 1 },
      } as PaginatedResponse<PortalCandidate>);

      portalService.updateCandidate.mockResolvedValue(mockPortalCandidate);
      portalService.getJobApplicationByReferenceKey.mockResolvedValue(null);
      portalService.createJobApplication.mockResolvedValue(mockJobApplication);
      portalService.getDocumentByReferenceKey.mockResolvedValue(existingDocument);
      portalService.updateDocument.mockResolvedValue(updatedDocument);
      portalService.updateJobApplication.mockResolvedValue(mockJobApplication);

      await service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions);

      expect(portalService.updateDocument).toHaveBeenCalledWith(mockTenant, 'existing-doc-123', {
        title: 'resume.pdf',
        documentType: 'cv',
        mimeType: 'application/pdf',
        data: mockDocumentBuffer.toString('base64'),
        byteSize: mockDocumentBuffer.length,
        reference: {
          old: 'reference',
          'test-key': 'test-value',
        },
      });

      expect(portalService.updateJobApplication).toHaveBeenCalledWith(mockTenant, 'job-app-123', {
        documentReferences: ['existing-doc-456', 'existing-doc-123'],
      });
    });

    it('should not duplicate document references', async () => {
      const mockAttachment = {
        id: 'doc-1',
        fileName: 'resume.pdf',
        downloadUrl: 'http://example.com/resume.pdf',
        type: 'cv' as const,
      };

      const mockDocumentBuffer = Buffer.from('document content');
      const mockDocument: PortalDocument = {
        id: 'existing-doc-123',
        title: 'resume.pdf',
        documentType: 'cv',
        mimeType: 'application/pdf',
        data: mockDocumentBuffer.toString('base64'),
        byteSize: mockDocumentBuffer.length,
        timestamp: new Date(),
        xfdf: '',
        reference: { 'test-key': 'test-value' },
      };

      const mockPortalCandidate: PortalCandidate = {
        id: 'portal-candidate-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        additionalData: {},
      } as PortalCandidate;

      const mockJobApplication: PortalJobApplication = {
        id: 'job-app-123',
        candidateId: 'portal-candidate-123',
        projectId: 'portal-project-123',
        active: true,
        documentReferences: ['existing-doc-123'], // Document already referenced
      } as PortalJobApplication;

      propertyResolver.getAttachments.mockReturnValue([mockAttachment]);
      source.getDocument.mockResolvedValue(mockDocumentBuffer);
      mimeTypeResolver.resolve.mockReturnValue('application/pdf');

      portalService.getCandidates.mockResolvedValue({
        data: [mockPortalCandidate],
        pagination: { page: 1, pageSize: 1, total: 1 },
      } as PaginatedResponse<PortalCandidate>);

      portalService.updateCandidate.mockResolvedValue(mockPortalCandidate);
      portalService.getJobApplicationByReferenceKey.mockResolvedValue(null);
      portalService.createJobApplication.mockResolvedValue(mockJobApplication);
      portalService.getDocumentByReferenceKey.mockResolvedValue(null);
      portalService.createDocument.mockResolvedValue(mockDocument);
      portalService.updateJobApplication.mockResolvedValue(mockJobApplication);

      await service.importCandidate(mockCandidate, mockJob, source, mockTenant, mockOptions);

      // Should not duplicate the document reference
      expect(portalService.updateJobApplication).toHaveBeenCalledWith(mockTenant, 'job-app-123', {
        documentReferences: ['existing-doc-123'], // No duplicates
      });
    });
  });
});
